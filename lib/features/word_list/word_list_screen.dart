import 'package:cussme/domain/domain.dart';
import 'package:cussme/features/profile_menu/profile_menu.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/routing/app_router.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'contract/word_list_intent.dart';
import 'contract/word_list_side_effects.dart';
import 'contract/word_list_state.dart';
import 'word_list_presenter.dart';

class WordListScreen extends ConsumerStatefulWidget {
  final LanguageEntity language;
  final String source;

  const WordListScreen({
    super.key,
    required this.language,
    required this.source,
  });

  @override
  ConsumerState<WordListScreen> createState() => _WordListScreenState();
}

class _WordListScreenState extends ConsumerState<WordListScreen>
    with RouteAware {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.unsubscribe(this);
    routeObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  void didPopNext() {
    super.didPopNext();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final presenter = ref.read(
        wordListPresenterProvider(language: widget.language, key: widget.key!)
            .notifier,
      );
      presenter.intentHandler(const WordListIntent.returned());
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(
        wordListPresenterProvider(language: widget.language, key: widget.key!));
    final presenter = ref.read(
        wordListPresenterProvider(language: widget.language, key: widget.key!)
            .notifier);
    _handleSideEffects(context);

    return AdMobScaffold(
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 16, top: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CussMeBackButton(
                      text: widget.source,
                      onPressed: () => presenter.intentHandler(
                        const WordListIntent.goBack(),
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.search, color: Palette.primary),
                      onPressed: () => presenter.intentHandler(
                        const WordListIntent.navigateToSearch(),
                      ),
                    ),
                    ProfileMenu(isGuest: state.isGuest),
                    const SizedBox(width: 4),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  widget.language.name,
                  style: TextStyles.titleLarge.copyWith(
                    color: Palette.black1d,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),
          if (!state.isInitialLoading)
            _buildSpicinessFilter(context, state, presenter),
          if (!state.isInitialLoading)
            const Divider(height: 1, color: Palette.outlineVariant),
          Expanded(
            child: state.isInitialLoading ||
                    state.loadingState != ScreenLoadingState.loaded
                ? ScreenLoading(
                    state: state.loadingState,
                    onRetry: () =>
                        presenter.intentHandler(const WordListIntent.retry()),
                  )
                : _buildWordsList(context, state, presenter),
          ),
        ],
      ),
    );
  }

  Widget _buildSpicinessFilter(
    BuildContext context,
    WordListState state,
    WordListPresenter presenter,
  ) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: Spiciness.values.map((spiciness) {
          return SpicinessChip(
            spiciness: spiciness,
            isSelected: state.selectedSpiciness.contains(spiciness),
            onSelected: (_) => presenter.intentHandler(
              WordListIntent.toggleSpiciness(spiciness),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildWordsList(
    BuildContext context,
    WordListState state,
    WordListPresenter presenter,
  ) {
    if (state.groupedWordItems.isEmpty) {
      return Center(
        child: Text(
          Str.of(context).bookmarksEmptyMessage,
          style: TextStyles.bodyLarge.copyWith(color: Palette.onSurface),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 8),
      itemCount: state.groupedWordItems.length,
      itemBuilder: (context, index) {
        final item = state.groupedWordItems[index];

        if (item is ListItemHeader) {
          return _buildHeaderItem(item.header);
        } else if (item is ListItemWord) {
          return WordListCardItem(
            word: item.word,
            onTap: () => presenter.intentHandler(
              WordListIntent.navigateToWordDetail(item.word.id),
            ),
            onBookmarkTap: () => presenter.intentHandler(
              WordListIntent.toggleBookmark(item.word),
            ),
            onPlayPronunciation: () =>
                presenter.intentHandler(PlayPronunciationIntent(item.word)),
          );
        } else if (item is ListItemAd) {
          return ResponsiveBannerAdWidget(
            key: ValueKey(item.key),
            padding: const EdgeInsets.symmetric(vertical: 16),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildHeaderItem(String header) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Container(
            width: 20,
            height: 20,
            margin: const EdgeInsets.only(top: 12),
            decoration: BoxDecoration(
              color: Palette.whiteD3,
              shape: BoxShape.circle,
              border: Border.all(color: Palette.secondary, width: 1),
            ),
            child: Center(
              child: Text(
                header,
                style: TextStyles.labelSmall.copyWith(
                  color: Palette.primary,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleSideEffects(BuildContext context) {
    ref.listen(
        wordListSideEffectsProvider(
            language: widget.language, key: widget.key!), (_, next) {
      final presenter = ref.read(
          wordListPresenterProvider(language: widget.language, key: widget.key!)
              .notifier);
      next.whenData((sideEffect) {
        switch (sideEffect) {
          case ShowMessageSideEffect _:
            Toast.show(context, sideEffect.message);
            break;
          case ShowMessageWithActionSideEffect _:
            Toast.showWithAction(
              context,
              message: sideEffect.message,
              actionLabel: sideEffect.actionText,
              onActionPressed: () => presenter
                  .intentHandler(const WordListIntent.navigateToBookmarks()),
            );
            break;
          case GoBackSideEffect _:
            context.pop();
            break;
          case final NavigateToWordDetailSideEffect se:
            GoRouter.of(context).pushToWordDetail(wordId: se.wordId);
            break;
          case NavigateToSignInSideEffect _:
            GoRouter.of(context).pushToSignIn();
            break;
          case NavigateToBookmarksSideEffect _:
            GoRouter.of(context).pushToBookmarks();
            break;
          case NavigateToSearchSideEffect _:
            GoRouter.of(context).pushToSearch();
            break;
        }
      });
    });
  }
}
